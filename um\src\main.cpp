#include "pch.h"

// Project includes
#include "window/window.hpp"
#include "driver/driver.hpp"
#include "driver/driver_manager.hpp"
#include "math/vector.hpp"
#include "render/render.hpp"

// Cheat functionality
#include "cheat/offsets.hpp"
#include "cheat/entity.hpp"
#include "cheat/gamevars.hpp"
#include "cheat/OffsetsUpdater.hpp"
#include "cheat/features/visuals/visuals.hpp"
#include "cheat/features/misc/misc.hpp"
#include "cheat/animations/ui/menu_animations.hpp"
#include "cheat/animations/core/animation_manager.hpp"

// Utils
#include "utils/getmodulebase.hpp"
#include "utils/getprocessid.hpp"
#include "config/config_manager.hpp"

bool InitializeDriver( HANDLE& driverHandle ) {
  createDriver();
  kdmap( 1, nullptr );
  remove( "km.sys" );

  driverHandle = CreateFile( _T("\\\\.\\SexyDriver"), GENERIC_READ, 0, nullptr, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr );
  if ( driverHandle == INVALID_HANDLE_VALUE ) {
    std::cout << "[-] Failed to create driver handle." << std::endl;
    return false;
  }
  return true;
}

bool InitializeProcess( DWORD& pid, HANDLE driverHandle ) {
  while ( ( pid = utils::get_process_id( L"cs2.exe" ) ) == 0 ) {
    std::cout << "[?] Waiting for cs2.exe..." << std::endl;
    std::this_thread::sleep_for( std::chrono::milliseconds( 1000 ) );
  }

  GameVars::getInstance()->setProcessId( pid );
  std::cout << "[+] Process found: " << std::hex << pid << std::endl;

  if ( !driver::attach_to_process( driverHandle, pid ) ) {
    std::cout << "[-] Failed to attach to process." << std::endl;
    return false;
  }
  std::cout << "[+] Attachment successful" << std::endl;
  return true;
}

bool InitializeModules() {
  uintptr_t clientBase = 0;
  while ( ( clientBase = utils::get_module_base( GameVars::getInstance()->getProcessId(), L"client.dll" ) ) == 0 ) {
    std::cout << "[?] Waiting for client.dll..." << std::endl;
    std::this_thread::sleep_for( std::chrono::milliseconds( 1000 ) );
  }
  GameVars::getInstance()->setClient( clientBase );
  std::cout << "[+] Client found at: 0x" << std::hex << clientBase << std::endl;
  return true;
}

bool UpdateOffsets() {
  try {
    const std::string baseUrl = "https://raw.githubusercontent.com/a2x/cs2-dumper/refs/heads/main/output/";
    const std::string clientSuffix = "client_dll.hpp";
    const std::string buttonSuffix = "buttons.hpp";
    const std::string offsetsSuffix = "offsets.hpp";

    std::string clientUrl = baseUrl + clientSuffix;
    std::string buttonUrl = baseUrl + buttonSuffix;
    std::string offsetsUrl = baseUrl + offsetsSuffix;

    useclientOffsets( parseOffsetsFromString( downloadFile( clientUrl ) ) );
    useOffsets( parseOffsetsFromString( downloadFile( offsetsUrl ) ) );
    usebuttonOffsets( parseOffsetsFromString( downloadFile( buttonUrl ) ) );

    return true;
  } catch ( const std::exception& e ) {
    std::cerr << "[-] Failed to update offsets: " << e.what() << std::endl;
    return false;
  }
}
#include "cheat/features/aimbot/aimbot.hpp"

inline Aimbot aimbot;

void MainLoop( Overlay& overlay, Reader& reader ) {
  while ( overlay.shouldRun ) {

    if (GetAsyncKeyState(VK_END) & 0x8000){
      exit(0);
    }

    // Update animations every frame - CRITICAL for health/armor bar animations
    AnimationManager::Update();

    // Run aimbot in main thread for better data access
    if (globals::Legitbot::enabled && GameData::isInitialized()) {
      aimbot.doAimbot();
    }

    overlay.StartRender();

    // Always call Render if menu should be visible or is animating
    if ( overlay.RenderMenu || MenuAnimations::IsMenuAnimating() ) {
      overlay.Render();
    }

    VISUALS::RenderESP( GameVars::getInstance()->getDriver(), reader );
    Misc::RenderMisc( reader );

    // Render keystrokes ALWAYS (independent of menu state)
    if (globals::Keystrokes::enabled) {
      Misc::Keystrokes(overlay.RenderMenu);
    }

    overlay.EndRender();
  }
}

int main() {
  DWORD   pid;
  HANDLE  driverHandle;
  Overlay overlay;
  Reader  reader;

  if ( !InitializeDriver( driverHandle ) ) {
    return -1;
  }
  GameVars::getInstance()->setDriver( driverHandle );

  if ( !InitializeProcess( pid, driverHandle ) ) {
    CloseHandle( driverHandle );
    return -1;
  }

  if ( !InitializeModules() ) {
    CloseHandle( driverHandle );
    return -1;
  }

  std::thread ReadEntitys( &Reader::ThreadEntitys, &reader );
  ReadEntitys.detach();

  std::thread ReadPlayers( &Reader::ThreadPlayers, &reader );
  ReadPlayers.detach();


  if ( !UpdateOffsets() ) {
    CloseHandle( driverHandle );
    return -1;
  }

  std::cout << "[!] Hit insert to show the menu in this overlay!\n";
  overlay.SetupOverlay( "totally not a cheat" );

  // Initialize configuration system
  std::cout << "[!] Initializing configuration system...\n";
  ConfigManager::EnsureConfigDirectories();
  std::cout << "[+] Configuration system initialized!\n";

  MainLoop( overlay, reader );

  // No automatic config saving on exit

  CloseHandle( driverHandle );
  return 0;
}